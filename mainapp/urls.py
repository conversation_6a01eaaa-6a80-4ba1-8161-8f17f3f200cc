from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView, TokenBlacklistView

from mainapp.views import test_api_get, test_api_post, signup, login, logout, dashboard, add_managed_subdomain, \
    connect_exising_domain, \
    check_nameserver_records, save_naming_strategy_and_contact_count, get_email_preview, webhook_forwardemail, \
    setup_email_aliases, email_accounts, create_campaign, campaigns, setup_campaign_emails, \
    generate_campaign_message_uid, save_campaign_setup_changes, start_campaign, pause_campaign, resume_campaign, \
    mark_email_sent, get_campaign_analytics, remove_managed_subdomain, get_schedule_email_status, cancel_campaign, \
    check_email_preview_generation_done, check_email_setup_done, campaign_email_reply_received_webhook, \
    save_email_s3_key, get_campaign_reply_to_address, campaign_unsubscribe_contact, get_campaign_details, \
    campaign_settings, mark_bad_email, get_campaign_schedules, get_campaign_ignored_emails, get_reply_email_message, \
    webhook_email_verify, get_campaign_leads, get_campaign_contacts, google_auth_callback, start_google_auth_flow, \
    integrations, google_auth_revoke, google_postmaster_integration, google_postmaster_add_domain, \
    google_postmaster_remove_domain, duplicate_campaign, delete_campaign_contact, schedule_campaign_start, \
    delete_campaign_custom_start_datetime, send_reset_password_link, change_password, workspaces, create_workspace, \
    get_settings, save_workspace_settings, switch_workspace, delete_workspace, domain_redirection_setup, \
    domain_redirection_delete, email_accounts__emails, send_test_email, faq_page, success_stage_data, \
    manage_subscription, subscription_history, base_page_data, start_checkout_session, start_stripe_customer_portal_session, \
    change_subscription_plan, signup_plan_selection, get_campaign_account_email, archive_campaign, \
    request_manual_approval, campaign_export_data, get_credit_history, get_update_post, resend_verification_email, \
    verify_email, contact_lists, add_contact_list, contact_list_details, create_campaign__fetch_contact_lists, \
    delete_contact_list, update_reply_classification, subscribe_and_unsubscribe, integration_request, \
    admin_add_managed_subdomain, admin_check_nameserver_records, \
    admin_save_naming_strategy_and_contact_count, admin_check_email_preview_generation_done, admin_get_email_preview, \
    admin_setup_email_aliases, admin_check_email_setup_done, admin_success_stage_data, sns_email_tracking, \
    get_campaign_open_events, get_link_click_events, sns_cloudfront_disabled_alert, get_hubspot_auth_url, hubspot_callback, \
    import_hubspot_contacts, get_hubspot_lists, delete_hubspot_integration, get_celery_task_progress, save_column_selection
from mainapp.views.views_admin import admin_dashboard, admin_get_spam_words, admin_delete_spam_word, \
    admin_delete_spam_words, admin_add_spam_words, admin_get_salutation_words, admin_delete_salutation_word, \
    admin_delete_salutation_words, admin_add_salutation_words, admin_all_users_page, admin_user_details, \
    admin_block_user_campaigns, admin_unblock_user_campaigns, admin_user_workspaces, admin_user_connected_domains, \
    admin_user_emails, admin_user_campaigns, admin_user_campaign_details, admin_cancel_campaign, \
    admin_all_subscription_plans, admin_create_new_subscription_plan, admin_edit_subscription_plan, \
    admin_approval_request, admin_add_update_post, admin_edit_update_post, admin_delete_udpate_post, \
    admin_get_update_post, admin_user_verify_email, admin_delete_connected_domain, \
    admin_update_warmup_date, admin_add_remove_email_credit
from mainapp.views.views_stripe import stripe_webhook, stripe_checkout_success, stripe_plan_update_success

urlpatterns = [
    # Admin
    path('admin/auth/', admin_dashboard, name='admin_auth'),
    path('admin/dashboard/', admin_dashboard, name='admin_dashboard'),
    path('admin/all-users/', admin_all_users_page, name='admin_all_users_page'),
    path('admin/get-user-details/', admin_user_details, name='admin_user_details'),
    path('admin/block-user-campaigns/', admin_block_user_campaigns, name='admin_block_user_campaigns'),
    path('admin/unblock-user-campaigns/', admin_unblock_user_campaigns, name='admin_unblock_user_campaigns'),
    path('admin/get-user-workspaces/', admin_user_workspaces, name='admin_user_workspaces'),
    path('admin/get-user-connected-domains/', admin_user_connected_domains, name='admin_user_connected_domains'),
    path('admin/delete-domain/', admin_delete_connected_domain, name='admin_delete_connected_domain'),
    path('admin/get-user-emails/', admin_user_emails, name='admin_user_emails'),
    path('admin/get-user-campaigns/', admin_user_campaigns, name='admin_user_campaigns'),
    path('admin/get-campaign-details/', admin_user_campaign_details, name='admin_user_campaign_details'),
    path('admin/cancel-campaign/', admin_cancel_campaign, name='admin_cancel_campaign'),
    path('admin/get-spam-words/', admin_get_spam_words, name='admin_get_spam_words'),
    path('admin/delete-spam-word/', admin_delete_spam_word, name='admin_delete_spam_word'),
    path('admin/delete-spam-words/', admin_delete_spam_words, name='admin_delete_spam_words'),
    path('admin/add-spam-words/', admin_add_spam_words, name='admin_add_spam_words'),
    path('admin/get-salutation-words/', admin_get_salutation_words, name='admin_get_salutation_words'),
    path('admin/delete-salutation-word/', admin_delete_salutation_word, name='admin_delete_salutation_word'),
    path('admin/delete-salutation-words/', admin_delete_salutation_words, name='admin_delete_salutation_words'),
    path('admin/add-salutation-words/', admin_add_salutation_words, name='admin_add_salutation_words'),
    path('admin/all-subscription-plans/', admin_all_subscription_plans, name='admin_all_subscription_plans'),
    path('admin/create-new-subscription-plan/', admin_create_new_subscription_plan,
         name='admin_create_new_subscription_plan'),
    path('admin/edit-subscription-plan/', admin_edit_subscription_plan, name='admin_edit_subscription_plan'),
    path('admin/email-content-approval-request/', admin_approval_request, name='admin_approval_request'),
    path('admin/admin-add-update-post/', admin_add_update_post, name='admin_add_update_post'),
    path('admin/admin-edit-update-post/', admin_edit_update_post, name='admin_edit_update_post'),
    path('admin/admin-delete-udpate-post/', admin_delete_udpate_post, name='admin_delete_udpate_post'),
    path('admin/admin-get-update-posts/', admin_get_update_post, name='admin_get_update_post'),
    path('admin/admin-user-verify-email/', admin_user_verify_email, name='admin_user_verify_email'),
    # path('admin/admin-get-all-campaign/',admin_get_all_campaign, name='admin_get_all_campaign'),
    path('admin/admin-update-warmup-date/', admin_update_warmup_date, name='admin_update_warmup_date'),
    path('admin/connect/add-managed-subdomain/', admin_add_managed_subdomain, name='admin_add_managed_subdomain'),
    path('admin/connect/check-ns-records/', admin_check_nameserver_records, name='admin_check_nameserver_records'),
    path('admin/connect/save-strategy-contact-count/', admin_save_naming_strategy_and_contact_count, name='admin_save_naming_strategy_and_contact_count'),
    path('admin/connect/check-email-generation-done/', admin_check_email_preview_generation_done, name='admin_check_email_preview_generation_done'),
    path('admin/connect/get-email-preview/', admin_get_email_preview, name='admin_get_email_preview'),
    path('admin/connect/setup-email-aliases/', admin_setup_email_aliases, name='admin_setup_email_aliases'),
    path('admin/connect/check-email-setup-done/', admin_check_email_setup_done, name='admin_check_email_setup_done'),
    path('admin/connect/success-stage-data/', admin_success_stage_data, name='admin_success_stage_data'),
    path('admin/admin-add-remove-email-credit/', admin_add_remove_email_credit, name='admin_add_remove_email_credit'),    

    # Auth URLs
    path('auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/token/blacklist/', TokenBlacklistView.as_view(), name='token_blacklist'),
    path('auth/signup/', signup, name='signup'),
    path('auth/login/', login, name='login'),
    path('auth/reset-password/send-link/', send_reset_password_link, name='send_reset_password_link'),
    path('auth/reset-password/change-password/', change_password, name='change_password'),
    path('auth/logout/', logout, name='logout'),

    path('onboarding/plan-selection/', signup_plan_selection, name='signup_plan_selection'),

    # Other Page Data and APIs
    path('logged-in-user-base-page/', base_page_data, name='base_page_data'),
    path('workspaces/', workspaces, name='workspaces'),
    path('workspaces/create/', create_workspace, name='create_workspace'),
    path('workspaces/switch/', switch_workspace, name='switch_workspace'),
    path('workspaces/delete/', delete_workspace, name='delete_workspace'),
    path('dashboard/', dashboard, name='dashboard'),
    path('settings/', get_settings, name='get_settings'),
    path('settings/save-workspace/', save_workspace_settings, name='save_workspace_settings'),
    path('connect-exising-domain/', connect_exising_domain, name='connect_exising_domain'),
    path('email-accounts/', email_accounts, name='email_accounts'),
    path('email-accounts/emails/', email_accounts__emails, name='email_accounts__emails'),
    path('contact-lists/', contact_lists, name='contact_lists'),
    path('contact-list-details/', contact_list_details, name='contact_list_details'),
    path('add-contact-list/', add_contact_list, name='add_contact_list'),
    path('delete-contact-list/', delete_contact_list, name='delete_contact_list'),
    path('integrations/', integrations, name='integrations'),
    path('send-test-email/', send_test_email, name='send_test_email'),
    path('faq/', faq_page, name='faq_page'),
    path('manage-subscription/', manage_subscription, name='manage_subscription'),
    path('subscription-history/', subscription_history, name='subscription_history'),
    path('start-checkout/', start_checkout_session, name='start_checkout_session'),
    path('get-stripe-portal-url/', start_stripe_customer_portal_session, name='start_stripe_customer_portal_session'),
    path('change-subscription/', change_subscription_plan, name='change_subscription_plan'),
    path('checkout-success/', stripe_checkout_success, name='checkout_success'),
    path('plan-update-success/', stripe_plan_update_success, name='plan_update_success'),

    # Connect Domain.
    path('connect/add-managed-subdomain/', add_managed_subdomain, name='add_managed_subdomain'),
    path('connect/check-ns-records/', check_nameserver_records, name='check_nameserver_records'),
    path('connect/save-strategy-contact-count/', save_naming_strategy_and_contact_count,
         name='save_naming_strategy_and_contact_count'),
    path('connect/check-email-generation-done/', check_email_preview_generation_done,
         name='check_email_preview_generation_done'),
    path('connect/get-email-preview/', get_email_preview, name='get_email_preview'),
    path('connect/setup-email-aliases/', setup_email_aliases, name='setup_email_aliases'),
    path('connect/check-email-setup-done/', check_email_setup_done, name='check_email_setup_done'),
    path('connect/remove-managed-subdomain/', remove_managed_subdomain, name='remove_managed_subdomain'),
    path('connect/success-stage-data/', success_stage_data, name='success_stage_data'),

    # Integrations.
    path('integrations/google-postmaster/', google_postmaster_integration, name='google_postmaster_integration'),
    path('integrations/google-postmaster/add/', google_postmaster_add_domain, name='google_postmaster_add_domain'),
    path('integrations/google-postmaster/remove/', google_postmaster_remove_domain,
         name='google_postmaster_remove_domain'),

    # Domain Redirect.
    path('domain-redirect/setup/', domain_redirection_setup, name='domain_redirection_setup'),
    path('domain-redirect/delete/', domain_redirection_delete, name='domain_redirection_delete'),

    # Campaigns.
    path('campaigns/get/', campaigns, name='campaigns'),
    path('campaigns/create/', create_campaign, name='create_campaign'),
    path('campaigns/create/fetch-contact-lists/', create_campaign__fetch_contact_lists,
         name='create_campaign__fetch_contact_lists'),
    path('campaigns/duplicate/', duplicate_campaign, name='duplicate_campaign'),
    path('campaigns/setup-emails/', setup_campaign_emails, name='setup_campaign_emails'),
    path('campaigns/generate-message-uid/', generate_campaign_message_uid, name='generate_campaign_message_uid'),
    path('campaigns/save-campaign-setup-changes/', save_campaign_setup_changes, name='save_campaign_setup_changes'),
    path('campaigns/request-content-approval/', request_manual_approval, name='request_manual_approval'),
    path('campaigns/contacts/delete/', delete_campaign_contact, name='delete_campaign_contact'),
    path('campaigns/scheduled-start/add/', schedule_campaign_start,
         name='set_campaign_custom_start_datetime'),
    path('campaigns/scheduled-start/delete/', delete_campaign_custom_start_datetime,
         name='delete_campaign_custom_start_datetime'),
    path('campaigns/start-campaign/', start_campaign, name='start_campaign'),
    path('campaigns/pause-campaign/', pause_campaign, name='pause_campaign'),
    path('campaigns/resume-campaign/', resume_campaign, name='resume_campaign'),
    path('campaigns/cancel-campaign/', cancel_campaign, name='cancel_campaign'),
    path('campaigns/archive-campaign/', archive_campaign, name='archive_campaign'),
    path('campaigns/mark-email-sent/', mark_email_sent, name='mark_email_sent'),
    path('campaigns/details/', get_campaign_details, name='get_campaign_details'),
    path('campaigns/details/analytics/', get_campaign_analytics, name='get_campaign_analytics'),
    path('campaigns/details/analytics/open-events/', get_campaign_open_events, name='get_campaign_open_events'),
    path('campaigns/details/analytics/click-events/', get_link_click_events, name='get_link_click_events'),
    path('campaigns/details/get-schedules/', get_campaign_schedules, name='get_campaign_schedules'),
    path('campaigns/details/get-contacts-tabs-data/', get_campaign_contacts, name='get_campaign_contacts'),
    path('campaigns/details/get-leads/', get_campaign_leads, name='get_campaign_leads'),
    path('campaigns/details/get-ignored-emails/', get_campaign_ignored_emails, name='get_campaign_schedules'),
    path('campaigns/get-schedule-email-status/', get_schedule_email_status, name='get_schedule_email_status'),
    path('campaigns/get-user-dest-email/', get_campaign_reply_to_address,
         name='get_user_destination_email_address'),
    path('campaigns/get-campaign-account-email/', get_campaign_account_email,
         name='get_campaign_account_email'),
    path('campaigns/unsubscribe/', campaign_unsubscribe_contact, name='campaign_unsubscribe_contact'),
    path('campaigns/mark-bad-email/', mark_bad_email, name='mark_bad_email'),
    path('campaigns/settings/', campaign_settings, name='campaign_settings'),
    path('campaigns/get-reply-email-message/', get_reply_email_message, name='get_reply_email_message'),
    # Export campaign
    path('campaigns/campaign_export_data/',campaign_export_data),

    # Schedule
    path('schedule/save-email-s3-key/', save_email_s3_key, name='save_email_s3_key'),

    # Webhooks
    path('webhook/forwardemail/', webhook_forwardemail, name='webhook_forwardemail'),
    path('webhook/reply-received/', campaign_email_reply_received_webhook,
         name='campaign_email_reply_received_webhook'),
    path('webhook/email-verify/', webhook_email_verify, name='webhook_email_verify'),
    path('webhook/stripe/', stripe_webhook, name='stripe_webhook'),
    path('webhook/email-tracking/', sns_email_tracking, name="sns_email_tracking"),
    path('webhook/cf-disabled-alert/', sns_cloudfront_disabled_alert, name="sns_cloudfront_disabled_alert"),

    # Google auth.
    path('google-auth/start/', start_google_auth_flow, name='start_google_auth_flow'),
    path('google-auth/callback/', google_auth_callback, name='google_auth_callback'),
    path('google-auth/revoke/', google_auth_revoke, name='google_auth_revoke'),

    # Misc.
    path('test/get/', test_api_get),
    path('test/post/', test_api_post),

    # create history
    path('credit-history/', get_credit_history, name='get_credit_history'),

    # update
    path('update/get-update-posts/', get_update_post, name='get_update_post'),

    # Email Verification
    path('resend-verification-email/', resend_verification_email, name='resend_verification_email'),
    path('verify-email/', verify_email, name="verify_email"),

    path("update-reply-classification/", update_reply_classification, name="update_reply_classification"),
    path("subscribe-and-unsubscribe/", subscribe_and_unsubscribe, name="subscribe_and_unsubscribe"),

    # Integation
    path('integration-request/', integration_request, name="integration_request"),
    path('hubspot/callback/', hubspot_callback, name="hubspot_callback"),
    path('hubspot/get-auth-url/', get_hubspot_auth_url, name="get_hubspot_auth_url"),
    path('hubspot/get-all-contact/', import_hubspot_contacts, name="import_hubspot_contacts"),
    path('hubspot/get-hubspot-lists/', get_hubspot_lists, name="get_hubspot_lists"),
    path('hubspot/delete-hubspot-integration/', delete_hubspot_integration, name="delete_hubspot_integration"),
    path('get-celery-task-progress/', get_celery_task_progress, name="get_celery_task_progress"),
    path('hubspot/save-column-selection/', save_column_selection, name="save_column_selection"),
]
